import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../core/widgets/app_text.dart';
import 'date_picker_bottom_sheet.dart';

class CreditPeriodBottomSheet extends StatefulWidget {
  final int selectedPeriod;
  final Function(int) onPeriodSelected;

  const CreditPeriodBottomSheet({
    super.key,
    required this.selectedPeriod,
    required this.onPeriodSelected,
  });

  @override
  State<CreditPeriodBottomSheet> createState() =>
      _CreditPeriodBottomSheetState();
}

class _CreditPeriodBottomSheetState extends State<CreditPeriodBottomSheet> {
  late int _selectedPeriod;
  DateTime? _selectedDate;
  bool _isCustomSelected = false;

  final List<int> _predefinedPeriods = [7, 15, 30, 45, 60, 90];

  @override
  void initState() {
    super.initState();
    _selectedPeriod = widget.selectedPeriod;
    _isCustomSelected = !_predefinedPeriods.contains(_selectedPeriod);
    if (_isCustomSelected) {
      // Calculate the date based on the selected period
      _selectedDate = DateTime.now().add(Duration(days: _selectedPeriod));
    }
  }

  void _handlePeriodSelection(int period) {
    setState(() {
      _selectedPeriod = period;
      _isCustomSelected = false;
    });
    HapticFeedback.selectionClick();
  }

  void _handleCustomSelection() async {
    HapticFeedback.selectionClick();

    final DateTime? picked = await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DatePickerBottomSheet(
        initialDate:
            _selectedDate ?? DateTime.now().add(const Duration(days: 7)),
        firstDate: DateTime.now().add(const Duration(days: 1)),
        lastDate: DateTime.now().add(const Duration(days: 365)),
      ),
    );

    if (picked != null) {
      // Normalize both dates to midnight to get accurate day difference
      final today = DateTime.now();
      final todayMidnight = DateTime(today.year, today.month, today.day);
      final pickedMidnight = DateTime(picked.year, picked.month, picked.day);

      final daysDifference = pickedMidnight.difference(todayMidnight).inDays;
      setState(() {
        _isCustomSelected = true;
        _selectedDate = picked;
        _selectedPeriod = daysDifference > 0 ? daysDifference : 1;
      });
    }
  }

  void _handleSave() {
    HapticFeedback.lightImpact();
    widget.onPeriodSelected(_selectedPeriod);
    Navigator.of(context).pop();
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];
    return '${months[date.month - 1]} ${date.day}, ${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: Color(0xFFE5E7EB), width: 1),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const AppText(
                    'Set Credit Period',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      Navigator.of(context).pop();
                    },
                    child: const Icon(
                      Icons.close,
                      size: 24,
                      color: Color(0xFF6B7280),
                    ),
                  ),
                ],
              ),
            ),

            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Predefined periods
                    ..._predefinedPeriods.map(
                      (period) => _buildPeriodOption(
                        '$period Days',
                        period,
                        _selectedPeriod == period && !_isCustomSelected,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Custom option
                    _buildCustomOption(),
                  ],
                ),
              ),
            ),

            // Save button
            Container(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _handleSave,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: const Color(0xFF5A67D8),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    elevation: 0,
                  ),
                  child: const AppText(
                    'Save',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPeriodOption(String label, int period, bool isSelected) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: () => _handlePeriodSelection(period),
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected
                  ? const Color(0xFF5A67D8)
                  : const Color(0xFFE5E7EB),
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              AppText(
                label,
                fontSize: 16,
                color: isSelected
                    ? const Color(0xFF5A67D8)
                    : const Color(0xFF374151),
              ),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: isSelected
                        ? const Color(0xFF5A67D8)
                        : const Color(0xFFD1D5DB),
                    width: 2,
                  ),
                  color: isSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.transparent,
                ),
                child: isSelected
                    ? const Icon(Icons.check, size: 12, color: Colors.white)
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomOption() {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: _handleCustomSelection,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            border: Border.all(
              color: _isCustomSelected
                  ? const Color(0xFF5A67D8)
                  : const Color(0xFFE5E7EB),
              width: _isCustomSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Row(
                  children: [
                    AppText(
                      'Custom Date',
                      fontSize: 16,
                      color: _isCustomSelected
                          ? const Color(0xFF5A67D8)
                          : const Color(0xFF374151),
                    ),
                    const SizedBox(width: 16),
                    if (_isCustomSelected && _selectedDate != null)
                      Expanded(
                        child: AppText(
                          _formatDate(_selectedDate!),
                          fontSize: 14,
                          color: const Color(0xFF5A67D8),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                  ],
                ),
              ),
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: _isCustomSelected
                        ? const Color(0xFF5A67D8)
                        : const Color(0xFFD1D5DB),
                    width: 2,
                  ),
                  color: _isCustomSelected
                      ? const Color(0xFF5A67D8)
                      : Colors.transparent,
                ),
                child: _isCustomSelected
                    ? const Icon(Icons.check, size: 12, color: Colors.white)
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
