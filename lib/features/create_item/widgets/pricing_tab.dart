import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/constants/app_colors.dart';
import '../bloc/create_item_bloc.dart';
import '../bloc/create_item_event.dart';
import '../bloc/create_item_state.dart';
import '../models/item.dart';

class PricingTab extends StatefulWidget {
  final CreateItemFormState state;
  final TextEditingController unitController;
  final TextEditingController salesPriceController;
  final TextEditingController purchasePriceController;
  final TextEditingController hsnController;

  const PricingTab({
    super.key,
    required this.state,
    required this.unitController,
    required this.salesPriceController,
    required this.purchasePriceController,
    required this.hsnController,
  });

  @override
  State<PricingTab> createState() => _PricingTabState();
}

class _PricingTabState extends State<PricingTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildUnitField(context, widget.state),
              const SizedBox(height: 16),
              _buildSalesPriceField(context, widget.state),
              const SizedBox(height: 16),
              _buildPurchasePriceField(context, widget.state),
              const SizedBox(height: 16),
              _buildGSTField(context, widget.state),
              const SizedBox(height: 16),
              _buildHSNField(context, widget.state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUnitField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Unit',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: widget.unitController,
          onChanged: (value) {
            context.read<CreateItemBloc>().add(UnitChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'PCS',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
        if (state.showValidationErrors && state.errors.containsKey('unit')) ...[
          const SizedBox(height: 4),
          AppText(state.errors['unit']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  Widget _buildSalesPriceField(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Sales Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: widget.salesPriceController,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  context.read<CreateItemBloc>().add(SalesPriceChanged(price));
                },
                decoration: InputDecoration(
                  prefixText: '₹ ',
                  hintText: '130',
                  hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(
                      color: AppColors.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildTaxDropdown(
              state.salesTaxType,
              (taxType) => context.read<CreateItemBloc>().add(
                SalesTaxTypeChanged(taxType),
              ),
            ),
          ],
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('salesPrice')) ...[
          const SizedBox(height: 4),
          AppText(state.errors['salesPrice']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  Widget _buildPurchasePriceField(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Purchase Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                controller: widget.purchasePriceController,
                keyboardType: TextInputType.number,
                onChanged: (value) {
                  final price = double.tryParse(value) ?? 0.0;
                  context.read<CreateItemBloc>().add(
                    PurchasePriceChanged(price),
                  );
                },
                decoration: InputDecoration(
                  prefixText: '₹ ',
                  hintText: '115',
                  hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: const BorderSide(
                      color: AppColors.primary,
                      width: 2,
                    ),
                  ),
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            _buildTaxDropdown(
              state.purchaseTaxType,
              (taxType) => context.read<CreateItemBloc>().add(
                PurchaseTaxTypeChanged(taxType),
              ),
            ),
          ],
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('purchasePrice')) ...[
          const SizedBox(height: 4),
          AppText(
            state.errors['purchasePrice']!,
            fontSize: 12,
            color: Colors.red,
          ),
        ],
      ],
    );
  }

  Widget _buildTaxDropdown(TaxType selectedType, Function(TaxType) onChanged) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<TaxType>(
        value: selectedType,
        onChanged: (TaxType? newValue) {
          if (newValue != null) {
            HapticFeedback.lightImpact();
            onChanged(newValue);
          }
        },
        underline: const SizedBox.shrink(),
        icon: const Icon(Icons.keyboard_arrow_down, color: AppColors.primary),
        items: TaxType.values.map<DropdownMenuItem<TaxType>>((TaxType value) {
          return DropdownMenuItem<TaxType>(
            value: value,
            child: AppText(
              value.displayName,
              fontSize: 12,
              color: Colors.black87,
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildGSTField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'GST',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<GSTType>(
            value: state.gstType,
            onChanged: (GSTType? newValue) {
              if (newValue != null) {
                HapticFeedback.lightImpact();
                context.read<CreateItemBloc>().add(GSTTypeChanged(newValue));
              }
            },
            underline: const SizedBox.shrink(),
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.primary,
            ),
            isExpanded: true,
            items: GSTType.values.map<DropdownMenuItem<GSTType>>((
              GSTType value,
            ) {
              return DropdownMenuItem<GSTType>(
                value: value,
                child: AppText(
                  value.displayName,
                  fontSize: 14,
                  color: Colors.black87,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildHSNField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'HSN',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: widget.hsnController,
          onChanged: (value) {
            context.read<CreateItemBloc>().add(HSNChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'Ex: 6704',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            suffixIcon: const Icon(Icons.search, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
