import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/constants/app_colors.dart';
import '../bloc/create_item_bloc.dart';
import '../bloc/create_item_event.dart';
import '../bloc/create_item_state.dart';
import '../models/item.dart';

class PricingTab extends StatefulWidget {
  final CreateItemFormState state;
  final TextEditingController unitController;
  final TextEditingController salesPriceController;
  final TextEditingController purchasePriceController;
  final TextEditingController hsnController;

  const PricingTab({
    super.key,
    required this.state,
    required this.unitController,
    required this.salesPriceController,
    required this.purchasePriceController,
    required this.hsnController,
  });

  @override
  State<PricingTab> createState() => _PricingTabState();
}

class _PricingTabState extends State<PricingTab> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildUnitField(context, widget.state),
              const SizedBox(height: 16),
              _buildSalesPriceField(context, widget.state),
              const SizedBox(height: 16),
              _buildPurchasePriceField(context, widget.state),
              const SizedBox(height: 16),
              _buildGSTField(context, widget.state),
              const SizedBox(height: 16),
              _buildHSNField(context, widget.state),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUnitField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Unit',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: widget.unitController,
          onChanged: (value) {
            context.read<CreateItemBloc>().add(UnitChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'PCS',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
        if (state.showValidationErrors && state.errors.containsKey('unit')) ...[
          const SizedBox(height: 4),
          AppText(state.errors['unit']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  Widget _buildSalesPriceField(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Sales Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: widget.salesPriceController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 0,
                      minHeight: 0,
                    ),
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 10,
                        bottom: 10,
                      ),
                      child: AppText(
                        '₹',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    hintText: '115',
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value) ?? 0.0;
                    context.read<CreateItemBloc>().add(
                      SalesPriceChanged(price),
                    );
                  },
                ),
              ),
              Builder(
                builder: (BuildContext buttonContext) {
                  return GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      _showSalesTaxMenu(buttonContext, state.salesTaxType);
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AppText(
                            state.salesTaxType.displayName,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('salesPrice')) ...[
          const SizedBox(height: 8),
          AppText(state.errors['salesPrice']!, fontSize: 12, color: Colors.red),
        ],
      ],
    );
  }

  Widget _buildPurchasePriceField(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'Purchase Price',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: Colors.grey[300]!),
          ),
          child: Row(
            children: [
              Expanded(
                child: TextFormField(
                  controller: widget.purchasePriceController,
                  keyboardType: const TextInputType.numberWithOptions(
                    decimal: true,
                  ),
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d*')),
                  ],
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: Colors.black87,
                  ),
                  decoration: InputDecoration(
                    isDense: true,
                    prefixIconConstraints: const BoxConstraints(
                      minWidth: 0,
                      minHeight: 0,
                    ),
                    prefixIcon: Padding(
                      padding: EdgeInsets.only(
                        left: 16,
                        right: 8,
                        top: 10,
                        bottom: 10,
                      ),
                      child: AppText(
                        '₹',
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    hintText: '115',
                    hintStyle: TextStyle(color: Colors.grey[400], fontSize: 16),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                  ),
                  onChanged: (value) {
                    final price = double.tryParse(value) ?? 0.0;
                    context.read<CreateItemBloc>().add(
                      PurchasePriceChanged(price),
                    );
                  },
                ),
              ),
              Builder(
                builder: (BuildContext buttonContext) {
                  return GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      _showPurchaseTaxMenu(
                        buttonContext,
                        state.purchaseTaxType,
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          AppText(
                            state.purchaseTaxType.displayName,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.black87,
                          ),
                          const SizedBox(width: 8),
                          Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.grey[600],
                            size: 20,
                          ),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        if (state.showValidationErrors &&
            state.errors.containsKey('purchasePrice')) ...[
          const SizedBox(height: 8),
          AppText(
            state.errors['purchasePrice']!,
            fontSize: 12,
            color: Colors.red,
          ),
        ],
      ],
    );
  }

  void _showSalesTaxMenu(BuildContext context, TaxType currentTaxType) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset(0, 30), ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset(0, 0)),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    final bloc = context.read<CreateItemBloc>();

    showMenu<TaxType>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      color: Colors.white,
      items: TaxType.values.map<PopupMenuItem<TaxType>>((TaxType value) {
        final bool isSelected = value == currentTaxType;
        return PopupMenuItem<TaxType>(
          value: value,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  isSelected
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  size: 16,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                ),
              ),
              const SizedBox(width: 12),
              AppText(
                value.displayName,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        );
      }).toList(),
    ).then((value) {
      if (value == null || !mounted) return;

      HapticFeedback.lightImpact();
      bloc.add(SalesTaxTypeChanged(value));
    });
  }

  void _showPurchaseTaxMenu(BuildContext context, TaxType currentTaxType) {
    final RenderBox button = context.findRenderObject() as RenderBox;
    final RenderBox overlay =
        Overlay.of(context).context.findRenderObject() as RenderBox;
    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(
        button.localToGlobal(Offset(0, 30), ancestor: overlay),
        button.localToGlobal(
          button.size.bottomRight(Offset(0, 0)),
          ancestor: overlay,
        ),
      ),
      Offset.zero & overlay.size,
    );

    final bloc = context.read<CreateItemBloc>();

    showMenu<TaxType>(
      context: context,
      position: position,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      elevation: 4,
      color: Colors.white,
      items: TaxType.values.map<PopupMenuItem<TaxType>>((TaxType value) {
        final bool isSelected = value == currentTaxType;
        return PopupMenuItem<TaxType>(
          value: value,
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: isSelected
                      ? const Color(0xFF5A67D8).withValues(alpha: 0.1)
                      : Colors.transparent,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  isSelected
                      ? Icons.check_circle
                      : Icons.radio_button_unchecked,
                  size: 16,
                  color: isSelected ? const Color(0xFF5A67D8) : Colors.grey,
                ),
              ),
              const SizedBox(width: 12),
              AppText(
                value.displayName,
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ],
          ),
        );
      }).toList(),
    ).then((value) {
      if (value == null || !mounted) return;

      HapticFeedback.lightImpact();
      bloc.add(PurchaseTaxTypeChanged(value));
    });
  }

  Widget _buildGSTField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'GST',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey[300]!),
            borderRadius: BorderRadius.circular(8),
          ),
          child: DropdownButton<GSTType>(
            value: state.gstType,
            onChanged: (GSTType? newValue) {
              if (newValue != null) {
                HapticFeedback.lightImpact();
                context.read<CreateItemBloc>().add(GSTTypeChanged(newValue));
              }
            },
            underline: const SizedBox.shrink(),
            icon: const Icon(
              Icons.keyboard_arrow_down,
              color: AppColors.primary,
            ),
            isExpanded: true,
            items: GSTType.values.map<DropdownMenuItem<GSTType>>((
              GSTType value,
            ) {
              return DropdownMenuItem<GSTType>(
                value: value,
                child: AppText(
                  value.displayName,
                  fontSize: 14,
                  color: Colors.black87,
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  Widget _buildHSNField(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const AppText(
          'HSN',
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: Colors.black87,
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: widget.hsnController,
          onChanged: (value) {
            context.read<CreateItemBloc>().add(HSNChanged(value));
          },
          decoration: InputDecoration(
            hintText: 'Ex: 6704',
            hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
            suffixIcon: const Icon(Icons.search, color: AppColors.primary),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey[300]!),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: const BorderSide(color: AppColors.primary, width: 2),
            ),
            contentPadding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
          ),
        ),
      ],
    );
  }
}
