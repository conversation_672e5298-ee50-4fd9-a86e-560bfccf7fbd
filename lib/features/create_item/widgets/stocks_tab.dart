import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/create_item_bloc.dart';
import '../bloc/create_item_event.dart';
import '../bloc/create_item_state.dart';

class StocksTab extends StatefulWidget {
  final CreateItemFormState state;
  final TextEditingController openingStockController;

  const StocksTab({
    super.key,
    required this.state,
    required this.openingStockController,
  });

  @override
  State<StocksTab> createState() => _StocksTabState();
}

class _StocksTabState extends State<StocksTab> {
  final _lowStockQuantityController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Initialize controllers with current state values
    widget.openingStockController.text = widget.state.openingStock > 0
        ? widget.state.openingStock.toString()
        : '';
    _lowStockQuantityController.text = widget.state.lowStockQuantity > 0
        ? widget.state.lowStockQuantity.toString()
        : '';
  }

  @override
  void dispose() {
    _lowStockQuantityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: AnimationLimiter(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: AnimationConfiguration.toStaggeredList(
            duration: const Duration(milliseconds: 300),
            childAnimationBuilder: (widget) => SlideAnimation(
              verticalOffset: 20.0,
              child: FadeInAnimation(child: widget),
            ),
            children: [
              _buildOpeningStockField(context, widget.state),
              const SizedBox(height: 16),
              _buildAsOfDateField(context, widget.state),
              const SizedBox(height: 24),
              _buildLowStockAlertSection(context, widget.state),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOpeningStockField(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const AppText(
                'Opening Stock',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: widget.openingStockController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*'),
                          ),
                        ],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Ex: 35',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 16,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 14,
                          ),
                        ),
                        onChanged: (value) {
                          final stock = double.tryParse(value) ?? 0.0;
                          context.read<CreateItemBloc>().add(
                            OpeningStockChanged(stock),
                          );
                        },
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: AppText(
                        state.unit.isEmpty ? 'BOX' : state.unit,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              if (state.showValidationErrors &&
                  state.errors.containsKey('openingStock')) ...[
                const SizedBox(height: 4),
                AppText(
                  state.errors['openingStock']!,
                  fontSize: 12,
                  color: Colors.red,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildAsOfDateField(BuildContext context, CreateItemFormState state) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const AppText(
                'As of Date',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 8),
              GestureDetector(
                onTap: () {
                  HapticFeedback.lightImpact();
                  _showDatePicker(context, state.asOfDate);
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 14,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.grey[300]!),
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: AppText(
                          _formatDate(state.asOfDate),
                          fontSize: 14,
                          color: Colors.black87,
                        ),
                      ),
                      Icon(
                        Icons.calendar_today,
                        color: const Color(0xFF5A67D8),
                        size: 20,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildLowStockAlertSection(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Low Stock Alert Toggle
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.notifications_outlined,
                color: const Color(0xFF5A67D8),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            const Expanded(
              child: AppText(
                'Low stock alert',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
            ),
            Switch.adaptive(
              value: state.lowStockAlert,
              onChanged: (value) {
                HapticFeedback.lightImpact();
                context.read<CreateItemBloc>().add(LowStockAlertChanged(value));
              },
              activeColor: const Color(0xFF5A67D8),
            ),
          ],
        ),

        // Low Stock Quantity Field (shown when alert is enabled)
        if (state.lowStockAlert) ...[
          const SizedBox(height: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const AppText(
                'Low Stock Quantity',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.black87,
              ),
              const SizedBox(height: 8),
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _lowStockQuantityController,
                        keyboardType: const TextInputType.numberWithOptions(
                          decimal: true,
                        ),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(r'^\d*\.?\d*'),
                          ),
                        ],
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Ex: 5',
                          hintStyle: TextStyle(
                            color: Colors.grey[400],
                            fontSize: 16,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 14,
                          ),
                        ),
                        onChanged: (value) {
                          final quantity = double.tryParse(value) ?? 0.0;
                          context.read<CreateItemBloc>().add(
                            LowStockQuantityChanged(quantity),
                          );
                        },
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[50],
                        borderRadius: const BorderRadius.only(
                          topRight: Radius.circular(12),
                          bottomRight: Radius.circular(12),
                        ),
                        border: Border(
                          left: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: AppText(
                        state.unit.isEmpty ? 'BOX' : state.unit,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                  ],
                ),
              ),
              if (state.showValidationErrors &&
                  state.errors.containsKey('lowStockQuantity')) ...[
                const SizedBox(height: 4),
                AppText(
                  state.errors['lowStockQuantity']!,
                  fontSize: 12,
                  color: Colors.red,
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  void _showDatePicker(BuildContext context, DateTime currentDate) async {
    final bloc = context.read<CreateItemBloc>();
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: currentDate,
      firstDate: DateTime(2000),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(
              context,
            ).colorScheme.copyWith(primary: const Color(0xFF5A67D8)),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != currentDate && mounted) {
      bloc.add(AsOfDateChanged(picked));
    }
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }
}
