import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import 'package:bill_book/core/constants/app_colors.dart';
import '../bloc/create_item_bloc.dart';
import '../bloc/create_item_event.dart';
import '../bloc/create_item_state.dart';
import '../models/item.dart';
import '../widgets/pricing_tab.dart';
import '../widgets/stocks_tab.dart';
import '../widgets/other_tab.dart';

class CreateItemScreen extends StatelessWidget {
  const CreateItemScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (_) => CreateItemBloc()..add(const InitializeCreateItem()),
      child: const CreateItemView(),
    );
  }
}

class CreateItemView extends StatefulWidget {
  const CreateItemView({super.key});

  @override
  State<CreateItemView> createState() => _CreateItemViewState();
}

class _CreateItemViewState extends State<CreateItemView>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final _nameController = TextEditingController();
  final _unitController = TextEditingController();
  final _salesPriceController = TextEditingController();
  final _purchasePriceController = TextEditingController();
  final _hsnController = TextEditingController();
  final _openingStockController = TextEditingController();
  final _itemDescriptionController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _unitController.dispose();
    _salesPriceController.dispose();
    _purchasePriceController.dispose();
    _hsnController.dispose();
    _openingStockController.dispose();
    _itemDescriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Dismiss keyboard when tapping outside of text fields
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: const Color(0xFFF5F5F5),
        body: SafeArea(
          child: BlocListener<CreateItemBloc, CreateItemState>(
            listener: (context, state) {
              if (state is CreateItemSuccess) {
                HapticFeedback.lightImpact();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: AppText(
                      'Item "${state.item.name}" created successfully!',
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    backgroundColor: Colors.green,
                    duration: const Duration(seconds: 2),
                  ),
                );
                Navigator.of(context).pop();
              } else if (state is CreateItemError) {
                HapticFeedback.lightImpact();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: AppText(
                      state.message,
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    backgroundColor: Colors.red,
                    duration: const Duration(seconds: 3),
                  ),
                );
              }
            },
            child: Column(
              children: [
                // Header
                _buildHeader(context),

                // Content
                Expanded(
                  child: BlocBuilder<CreateItemBloc, CreateItemState>(
                    builder: (context, state) {
                      if (state is CreateItemLoading) {
                        return const Center(
                          child: CircularProgressIndicator(
                            color: AppColors.primary,
                          ),
                        );
                      } else if (state is CreateItemFormState) {
                        return _buildFormContent(context, state);
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return AnimationConfiguration.staggeredList(
      position: 0,
      duration: const Duration(milliseconds: 400),
      child: SlideAnimation(
        verticalOffset: -30.0,
        curve: Curves.easeOutCubic,
        child: FadeInAnimation(
          curve: Curves.easeOutCubic,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.05),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    Navigator.of(context).pop();
                  },
                  style: const ButtonStyle(
                    padding: WidgetStatePropertyAll(EdgeInsets.zero),
                    minimumSize: WidgetStatePropertyAll(Size.zero),
                    visualDensity: VisualDensity.compact,
                  ),
                  icon: Icon(
                    Platform.isIOS
                        ? Icons.arrow_back_ios_new_rounded
                        : Icons.arrow_back,
                    color: Colors.black,
                  ),
                ),
                const Expanded(
                  child: AppText(
                    'Create New Item',
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: Colors.black,
                    textAlign: TextAlign.center,
                  ),
                ),
                IconButton(
                  onPressed: () {
                    HapticFeedback.lightImpact();
                    // TODO: Show settings or help
                  },
                  icon: const Icon(Icons.settings, color: Colors.grey),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildFormContent(BuildContext context, CreateItemFormState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Item Name Field
        AnimationConfiguration.staggeredList(
          position: 1,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildItemNameField(context, state),
            ),
          ),
        ),

        // Item Type Selection
        AnimationConfiguration.staggeredList(
          position: 2,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildItemTypeSelection(context, state),
            ),
          ),
        ),

        // Tab Bar
        AnimationConfiguration.staggeredList(
          position: 3,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildTabBar(),
            ),
          ),
        ),

        // Tab Content
        Expanded(
          child: AnimationConfiguration.staggeredList(
            position: 4,
            duration: const Duration(milliseconds: 300),
            child: SlideAnimation(
              verticalOffset: 30.0,
              curve: Curves.easeOutCubic,
              child: FadeInAnimation(
                curve: Curves.easeOutCubic,
                child: _buildTabContent(context, state),
              ),
            ),
          ),
        ),

        // Save Button
        AnimationConfiguration.staggeredList(
          position: 5,
          duration: const Duration(milliseconds: 300),
          child: SlideAnimation(
            verticalOffset: 30.0,
            curve: Curves.easeOutCubic,
            child: FadeInAnimation(
              curve: Curves.easeOutCubic,
              child: _buildSaveButton(context, state),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildItemNameField(BuildContext context, CreateItemFormState state) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const AppText(
                'Item Name ',
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Colors.black87,
              ),
              const AppText(
                '*',
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Colors.red,
              ),
            ],
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _nameController,
            onChanged: (value) {
              context.read<CreateItemBloc>().add(ItemNameChanged(value));
            },
            decoration: InputDecoration(
              hintText: 'Ex: Kissan Fruit Jam 500 gm',
              hintStyle: TextStyle(color: Colors.grey[400], fontSize: 14),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: Colors.grey[300]!),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(
                  color: AppColors.primary,
                  width: 2,
                ),
              ),
              errorBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: const BorderSide(color: Colors.red),
              ),
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
            ),
          ),
          if (state.showValidationErrors &&
              state.errors.containsKey('name')) ...[
            const SizedBox(height: 4),
            AppText(state.errors['name']!, fontSize: 12, color: Colors.red),
          ],
        ],
      ),
    );
  }

  Widget _buildItemTypeSelection(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const AppText(
            'Item Type',
            fontSize: 14,
            fontWeight: FontWeight.normal,
            color: Colors.black87,
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildTypeButton(
                context,
                'Product',
                ItemType.product,
                state.type == ItemType.product,
              ),
              const SizedBox(width: 12),
              _buildTypeButton(
                context,
                'Service',
                ItemType.service,
                state.type == ItemType.service,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTypeButton(
    BuildContext context,
    String label,
    ItemType type,
    bool isSelected,
  ) {
    return GestureDetector(
      onTap: () {
        HapticFeedback.lightImpact();
        context.read<CreateItemBloc>().add(ItemTypeChanged(type));
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected ? AppColors.primary : Colors.grey[300]!,
          ),
        ),
        child: AppText(
          label,
          fontSize: 14,
          fontWeight: FontWeight.w500,
          color: isSelected ? Colors.white : Colors.grey[700],
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      color: Colors.white,
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: const Color(0xFF5A67D8),
        unselectedLabelColor: Colors.grey[600],
        indicatorColor: const Color(0xFF5A67D8),
        indicatorWeight: 3,
        tabAlignment: TabAlignment.start,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        unselectedLabelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w500,
        ),
        tabs: const [
          Tab(text: 'Pricing'),
          Tab(text: 'Stock'),
          Tab(text: 'Other'),
          Tab(text: 'Party Wise Prices'),
        ],
      ),
    );
  }

  Widget _buildTabContent(BuildContext context, CreateItemFormState state) {
    return Container(
      color: Colors.white,
      child: TabBarView(
        controller: _tabController,
        children: [
          PricingTab(
            state: state,
            unitController: _unitController,
            salesPriceController: _salesPriceController,
            purchasePriceController: _purchasePriceController,
            hsnController: _hsnController,
          ),
          StocksTab(
            state: state,
            openingStockController: _openingStockController,
          ),
          _buildOtherTab(context, state),
          _buildPartyWisePricesTab(context, state),
        ],
      ),
    );
  }

  Widget _buildOtherTab(BuildContext context, CreateItemFormState state) {
    return OtherTab(
      state: state,
      itemDescriptionController: _itemDescriptionController,
    );
  }

  Widget _buildPartyWisePricesTab(
    BuildContext context,
    CreateItemFormState state,
  ) {
    return const Center(
      child: AppText(
        'Party Wise Prices tab content coming soon',
        fontSize: 16,
        color: Colors.grey,
      ),
    );
  }

  Widget _buildSaveButton(BuildContext context, CreateItemFormState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          width: double.infinity,
          height: 50,
          child: ElevatedButton(
            onPressed: state.isSubmitting
                ? null
                : () {
                    HapticFeedback.lightImpact();
                    context.read<CreateItemBloc>().add(const SaveItem());
                  },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 0,
            ),
            child: state.isSubmitting
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const AppText(
                    'Save',
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
          ),
        ),
      ),
    );
  }
}
