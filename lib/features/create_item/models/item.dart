import 'package:equatable/equatable.dart';

enum ItemType {
  product,
  service,
}

enum TaxType {
  withoutTax,
  withTax,
}

enum GSTType {
  none,
  gst5,
  gst12,
  gst18,
  gst28,
}

class Item extends Equatable {
  final String id;
  final String name;
  final ItemType type;
  final String unit;
  final double salesPrice;
  final TaxType salesTaxType;
  final double purchasePrice;
  final TaxType purchaseTaxType;
  final GSTType gstType;
  final String hsn;

  const Item({
    required this.id,
    required this.name,
    required this.type,
    required this.unit,
    required this.salesPrice,
    required this.salesTaxType,
    required this.purchasePrice,
    required this.purchaseTaxType,
    required this.gstType,
    required this.hsn,
  });

  Item copyWith({
    String? id,
    String? name,
    ItemType? type,
    String? unit,
    double? salesPrice,
    TaxType? salesTaxType,
    double? purchasePrice,
    TaxType? purchaseTaxType,
    GSTType? gstType,
    String? hsn,
  }) {
    return Item(
      id: id ?? this.id,
      name: name ?? this.name,
      type: type ?? this.type,
      unit: unit ?? this.unit,
      salesPrice: salesPrice ?? this.salesPrice,
      salesTaxType: salesTaxType ?? this.salesTaxType,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseTaxType: purchaseTaxType ?? this.purchaseTaxType,
      gstType: gstType ?? this.gstType,
      hsn: hsn ?? this.hsn,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type.name,
      'unit': unit,
      'salesPrice': salesPrice,
      'salesTaxType': salesTaxType.name,
      'purchasePrice': purchasePrice,
      'purchaseTaxType': purchaseTaxType.name,
      'gstType': gstType.name,
      'hsn': hsn,
    };
  }

  factory Item.fromJson(Map<String, dynamic> json) {
    return Item(
      id: json['id'] as String,
      name: json['name'] as String,
      type: ItemType.values.firstWhere((e) => e.name == json['type']),
      unit: json['unit'] as String,
      salesPrice: (json['salesPrice'] as num).toDouble(),
      salesTaxType: TaxType.values.firstWhere((e) => e.name == json['salesTaxType']),
      purchasePrice: (json['purchasePrice'] as num).toDouble(),
      purchaseTaxType: TaxType.values.firstWhere((e) => e.name == json['purchaseTaxType']),
      gstType: GSTType.values.firstWhere((e) => e.name == json['gstType']),
      hsn: json['hsn'] as String,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        type,
        unit,
        salesPrice,
        salesTaxType,
        purchasePrice,
        purchaseTaxType,
        gstType,
        hsn,
      ];
}

// Extension methods for display
extension ItemTypeExtension on ItemType {
  String get displayName {
    switch (this) {
      case ItemType.product:
        return 'Product';
      case ItemType.service:
        return 'Service';
    }
  }
}

extension TaxTypeExtension on TaxType {
  String get displayName {
    switch (this) {
      case TaxType.withoutTax:
        return 'Without Tax';
      case TaxType.withTax:
        return 'With Tax';
    }
  }
}

extension GSTTypeExtension on GSTType {
  String get displayName {
    switch (this) {
      case GSTType.none:
        return 'None';
      case GSTType.gst5:
        return '5% GST';
      case GSTType.gst12:
        return '12% GST';
      case GSTType.gst18:
        return '18% GST';
      case GSTType.gst28:
        return '28% GST';
    }
  }
}
