import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class CreateItemEvent extends Equatable {
  const CreateItemEvent();

  @override
  List<Object?> get props => [];
}

class InitializeCreateItem extends CreateItemEvent {
  const InitializeCreateItem();
}

class ItemNameChanged extends CreateItemEvent {
  final String name;

  const ItemNameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class ItemTypeChanged extends CreateItemEvent {
  final ItemType type;

  const ItemTypeChanged(this.type);

  @override
  List<Object?> get props => [type];
}

class UnitChanged extends CreateItemEvent {
  final String unit;

  const UnitChanged(this.unit);

  @override
  List<Object?> get props => [unit];
}

class SalesPriceChanged extends CreateItemEvent {
  final double price;

  const SalesPriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class SalesTaxTypeChanged extends CreateItemEvent {
  final TaxType taxType;

  const SalesTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class PurchasePriceChanged extends CreateItemEvent {
  final double price;

  const PurchasePriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class PurchaseTaxTypeChanged extends CreateItemEvent {
  final TaxType taxType;

  const PurchaseTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class GSTTypeChanged extends CreateItemEvent {
  final GSTType gstType;

  const GSTTypeChanged(this.gstType);

  @override
  List<Object?> get props => [gstType];
}

class HSNChanged extends CreateItemEvent {
  final String hsn;

  const HSNChanged(this.hsn);

  @override
  List<Object?> get props => [hsn];
}

class OpeningStockChanged extends CreateItemEvent {
  final double openingStock;

  const OpeningStockChanged(this.openingStock);

  @override
  List<Object?> get props => [openingStock];
}

class AsOfDateChanged extends CreateItemEvent {
  final DateTime asOfDate;

  const AsOfDateChanged(this.asOfDate);

  @override
  List<Object?> get props => [asOfDate];
}

class LowStockAlertChanged extends CreateItemEvent {
  final bool lowStockAlert;

  const LowStockAlertChanged(this.lowStockAlert);

  @override
  List<Object?> get props => [lowStockAlert];
}

class LowStockQuantityChanged extends CreateItemEvent {
  final double lowStockQuantity;

  const LowStockQuantityChanged(this.lowStockQuantity);

  @override
  List<Object?> get props => [lowStockQuantity];
}

class SaveItem extends CreateItemEvent {
  const SaveItem();
}

class ValidateForm extends CreateItemEvent {
  const ValidateForm();
}
