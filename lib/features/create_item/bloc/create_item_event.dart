import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class CreateItemEvent extends Equatable {
  const CreateItemEvent();

  @override
  List<Object?> get props => [];
}

class InitializeCreateItem extends CreateItemEvent {
  const InitializeCreateItem();
}

class ItemNameChanged extends CreateItemEvent {
  final String name;

  const ItemNameChanged(this.name);

  @override
  List<Object?> get props => [name];
}

class ItemTypeChanged extends CreateItemEvent {
  final ItemType type;

  const ItemTypeChanged(this.type);

  @override
  List<Object?> get props => [type];
}

class UnitChanged extends CreateItemEvent {
  final String unit;

  const UnitChanged(this.unit);

  @override
  List<Object?> get props => [unit];
}

class SalesPriceChanged extends CreateItemEvent {
  final double price;

  const SalesPriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class SalesTaxTypeChanged extends CreateItemEvent {
  final TaxType taxType;

  const SalesTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class PurchasePriceChanged extends CreateItemEvent {
  final double price;

  const PurchasePriceChanged(this.price);

  @override
  List<Object?> get props => [price];
}

class PurchaseTaxTypeChanged extends CreateItemEvent {
  final TaxType taxType;

  const PurchaseTaxTypeChanged(this.taxType);

  @override
  List<Object?> get props => [taxType];
}

class GSTTypeChanged extends CreateItemEvent {
  final GSTType gstType;

  const GSTTypeChanged(this.gstType);

  @override
  List<Object?> get props => [gstType];
}

class HSNChanged extends CreateItemEvent {
  final String hsn;

  const HSNChanged(this.hsn);

  @override
  List<Object?> get props => [hsn];
}

class SaveItem extends CreateItemEvent {
  const SaveItem();
}

class ValidateForm extends CreateItemEvent {
  const ValidateForm();
}
