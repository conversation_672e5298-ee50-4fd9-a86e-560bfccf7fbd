import 'package:equatable/equatable.dart';
import '../models/item.dart';

abstract class CreateItemState extends Equatable {
  const CreateItemState();

  @override
  List<Object?> get props => [];
}

class CreateItemInitial extends CreateItemState {
  const CreateItemInitial();
}

class CreateItemLoading extends CreateItemState {
  const CreateItemLoading();
}

class CreateItemFormState extends CreateItemState {
  final String name;
  final ItemType type;
  final String unit;
  final double salesPrice;
  final TaxType salesTaxType;
  final double purchasePrice;
  final TaxType purchaseTaxType;
  final GSTType gstType;
  final String hsn;
  final Map<String, String> errors;
  final bool isValid;
  final bool isSubmitting;
  final bool showValidationErrors;

  const CreateItemFormState({
    required this.name,
    required this.type,
    required this.unit,
    required this.salesPrice,
    required this.salesTaxType,
    required this.purchasePrice,
    required this.purchaseTaxType,
    required this.gstType,
    required this.hsn,
    required this.errors,
    required this.isValid,
    required this.isSubmitting,
    required this.showValidationErrors,
  });

  CreateItemFormState copyWith({
    String? name,
    ItemType? type,
    String? unit,
    double? salesPrice,
    TaxType? salesTaxType,
    double? purchasePrice,
    TaxType? purchaseTaxType,
    GSTType? gstType,
    String? hsn,
    Map<String, String>? errors,
    bool? isValid,
    bool? isSubmitting,
    bool? showValidationErrors,
  }) {
    return CreateItemFormState(
      name: name ?? this.name,
      type: type ?? this.type,
      unit: unit ?? this.unit,
      salesPrice: salesPrice ?? this.salesPrice,
      salesTaxType: salesTaxType ?? this.salesTaxType,
      purchasePrice: purchasePrice ?? this.purchasePrice,
      purchaseTaxType: purchaseTaxType ?? this.purchaseTaxType,
      gstType: gstType ?? this.gstType,
      hsn: hsn ?? this.hsn,
      errors: errors ?? this.errors,
      isValid: isValid ?? this.isValid,
      isSubmitting: isSubmitting ?? this.isSubmitting,
      showValidationErrors: showValidationErrors ?? this.showValidationErrors,
    );
  }

  @override
  List<Object?> get props => [
    name,
    type,
    unit,
    salesPrice,
    salesTaxType,
    purchasePrice,
    purchaseTaxType,
    gstType,
    hsn,
    errors,
    isValid,
    isSubmitting,
    showValidationErrors,
  ];
}

class CreateItemSuccess extends CreateItemState {
  final Item item;

  const CreateItemSuccess(this.item);

  @override
  List<Object?> get props => [item];
}

class CreateItemError extends CreateItemState {
  final String message;

  const CreateItemError(this.message);

  @override
  List<Object?> get props => [message];
}
