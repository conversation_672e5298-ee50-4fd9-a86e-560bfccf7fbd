import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:bill_book/core/widgets/app_text.dart';
import '../bloc/adjust_stock_bloc.dart';
import '../bloc/adjust_stock_event.dart';
import '../bloc/adjust_stock_state.dart';
import 'date_picker_bottom_sheet.dart';

class DatePickerField extends StatelessWidget {
  const DatePickerField({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AdjustStockBloc, AdjustStockState>(
      buildWhen: (previous, current) =>
          previous.selectedDate != current.selectedDate,
      builder: (context, state) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const AppText(
              'Date',
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: Colors.grey,
            ),
            const SizedBox(height: 8),
            GestureDetector(
              onTap: () => _showDatePicker(context, state.selectedDate),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(color: Colors.grey[300]!),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.03),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: AppText(
                        DateFormat('dd MMM yyyy').format(state.selectedDate),
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                    ),
                    Container(
                      width: 32,
                      height: 32,
                      decoration: BoxDecoration(
                        color: const Color(0xFF5A67D8).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: const Icon(
                        Icons.calendar_today_outlined,
                        color: Color(0xFF5A67D8),
                        size: 18,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showDatePicker(BuildContext context, DateTime currentDate) async {
    HapticFeedback.lightImpact();

    final DateTime? selectedDate = await showModalBottomSheet<DateTime>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DatePickerBottomSheet(
        initialDate: currentDate,
        firstDate: DateTime(2020),
        lastDate: DateTime.now(),
      ),
    );

    if (selectedDate != null && context.mounted) {
      HapticFeedback.mediumImpact();
      context.read<AdjustStockBloc>().add(UpdateDate(selectedDate));
    }
  }
}
